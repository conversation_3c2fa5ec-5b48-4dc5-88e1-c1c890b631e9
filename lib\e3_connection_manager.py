#!/usr/bin/env python3
"""
E3 Connection Manager

This module provides functionality to detect multiple E3.series instances and allow
users to select which instance to connect to. It returns the PID of the selected
instance, allowing individual scripts to handle their own connections.

Features:
- Detects multiple running E3.series instances
- Shows a GUI selector when multiple instances are found
- Returns PID of selected instance for script-specific connection handling
- Attempts to extract project names from window titles
- Reusable across different E3 automation scripts

Usage:
    from lib.e3_connection_manager import get_e3_instance_pid
    
    # Get PID of selected E3 instance
    pid = get_e3_instance_pid()
    if pid:
        # Use PID in your script's connection logic
        app = connect_to_e3_instance(pid)

Author: <PERSON>
Date: 2025-07-15
"""

import logging
import sys
import subprocess
from typing import Optional, List
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk


class E3InstanceInfo:
    """Information about an E3.series instance"""
    
    def __init__(self, process_id: int, window_title: str = "", project_name: str = ""):
        self.process_id = process_id
        self.window_title = window_title
        self.project_name = project_name
        
    def __str__(self):
        if self.project_name and self.project_name not in ["", "Unknown Project", "No Project"]:
            return f"PID {self.process_id}: {self.project_name}"
        elif self.project_name == "No Project":
            return f"PID {self.process_id}: (No Project Open)"
        elif self.project_name == "Project Open":
            return f"PID {self.process_id}: (Project Open - Name Unknown)"
        else:
            return f"PID {self.process_id}: E3.series Instance"


class E3InstanceSelector:
    """GUI for selecting an E3.series instance when multiple are running"""

    def __init__(self, instances: List[E3InstanceInfo], logger: Optional[logging.Logger] = None):
        self.instances = instances
        self.selected_instance = None
        self.root = None
        self.logger = logger or logging.getLogger(__name__)

    def show_selector(self) -> Optional[E3InstanceInfo]:
        """Show the instance selector dialog and return the selected instance"""
        # Set up CustomTkinter theme to match project style
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        self.root = ctk.CTk()
        self.root.title("E3.series Connection Manager")
        self.root.geometry("700x500")
        self.root.resizable(False, False)

        # Make window modal and center it
        self.root.grab_set()
        self.root.focus_set()
        self._center_window()

        # Create widgets
        self._create_widgets()

        # Run the dialog
        self.root.mainloop()

        return self.selected_instance
    
    def _center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """Create the dialog widgets using CustomTkinter"""
        # Configure grid layout
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=0)  # Header
        self.root.grid_rowconfigure(1, weight=1)  # List
        self.root.grid_rowconfigure(2, weight=0)  # Buttons

        # Header frame
        header_frame = ctk.CTkFrame(self.root)
        header_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")

        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="Multiple E3.series instances detected",
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=(20, 10))

        # Instructions
        instruction_label = ctk.CTkLabel(
            header_frame,
            text="Please select which E3.series instance to connect to:",
            font=("Arial", 12)
        )
        instruction_label.pack(pady=(0, 20))

        # List frame
        list_frame = ctk.CTkFrame(self.root)
        list_frame.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(1, weight=1)

        # List label
        list_label = ctk.CTkLabel(
            list_frame,
            text="Available E3.series Instances:",
            font=("Arial", 14, "bold"),
            anchor="w"
        )
        list_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")

        # Create a custom listbox using CTkScrollableFrame
        self.scrollable_frame = ctk.CTkScrollableFrame(
            list_frame,
            height=250
        )
        self.scrollable_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        self.scrollable_frame.grid_columnconfigure(0, weight=1)

        # Track selected instance
        self.selected_index = 0 if self.instances else -1
        self.instance_buttons = []
        self.instance_labels = []  # Store label references for color updates

        # Create clickable list items
        for i, instance in enumerate(self.instances):
            # Create button that looks like a list item
            instance_btn = ctk.CTkButton(
                self.scrollable_frame,
                text="",  # We'll set this with custom layout
                command=lambda idx=i: self._select_instance(idx),
                height=80,
                fg_color="transparent",
                hover_color="#404040",
                border_width=1,
                border_color="#404040",
                anchor="w"
            )
            instance_btn.grid(row=i, column=0, padx=5, pady=5, sticky="ew")

            # Create custom content frame inside the button
            content_frame = ctk.CTkFrame(instance_btn, fg_color="transparent")
            content_frame.place(relx=0, rely=0, relwidth=1, relheight=1)
            content_frame.grid_columnconfigure(0, weight=1)

            # Process ID and main info
            main_info = f"PID {instance.process_id}"
            main_label = ctk.CTkLabel(
                content_frame,
                text=main_info,
                font=("Arial", 14, "bold"),
                anchor="w"
            )
            main_label.grid(row=0, column=0, padx=20, pady=(12, 4), sticky="ew")

            # Project name or status
            if instance.project_name and instance.project_name not in ["Unknown Project", "No Project", "E3.series Running"]:
                project_text = f"Project: {instance.project_name}"
                text_color = "#4CAF50"  # Green for project names
            else:
                status_text = instance.project_name if instance.project_name else "No Project Open"
                project_text = f"Status: {status_text}"
                text_color = "#FFA500"  # Orange for status

            project_label = ctk.CTkLabel(
                content_frame,
                text=project_text,
                font=("Arial", 12),
                anchor="w",
                text_color=text_color
            )
            project_label.grid(row=1, column=0, padx=20, pady=(0, 12), sticky="ew")

            # Store button and label references
            self.instance_buttons.append(instance_btn)
            self.instance_labels.append({
                'main': main_label,
                'project': project_label,
                'project_color': text_color
            })

            # Make labels clickable too
            main_label.bind("<Button-1>", lambda e, idx=i: self._select_instance(idx))
            project_label.bind("<Button-1>", lambda e, idx=i: self._select_instance(idx))

        # Select first item by default
        if self.instances:
            self._select_instance(0)

        # Button frame
        button_frame = ctk.CTkFrame(self.root)
        button_frame.grid(row=2, column=0, padx=20, pady=(10, 20), sticky="ew")
        button_frame.grid_columnconfigure((0, 1), weight=1)

        # Connect button
        connect_button = ctk.CTkButton(
            button_frame,
            text="Connect",
            command=self._on_ok,
            width=140,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#C53F3F",
            hover_color="#A02222"
        )
        connect_button.grid(row=0, column=0, padx=10, pady=20)

        # Cancel button
        cancel_button = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self._on_cancel,
            width=140,
            height=40,
            font=("Arial", 14),
            fg_color="#666666",
            hover_color="#555555"
        )
        cancel_button.grid(row=0, column=1, padx=10, pady=20)

        # Bind Enter key to Connect
        self.root.bind("<Return>", lambda e: self._on_ok())
        self.root.bind("<Escape>", lambda e: self._on_cancel())

    def _select_instance(self, index):
        """Select an instance and update visual feedback"""
        if 0 <= index < len(self.instances):
            # Update selected index
            self.selected_index = index

            # Update button appearances and label colors
            for i, (btn, labels) in enumerate(zip(self.instance_buttons, self.instance_labels)):
                if i == index:
                    # Selected item - red background with white text
                    btn.configure(
                        fg_color="#C53F3F",
                        hover_color="#A02222",
                        border_color="#C53F3F"
                    )
                    labels['main'].configure(text_color="white")
                    labels['project'].configure(text_color="white")
                else:
                    # Unselected items - transparent background with original colors
                    btn.configure(
                        fg_color="transparent",
                        hover_color="#404040",
                        border_color="#404040"
                    )
                    labels['main'].configure(text_color="white")  # Default text color
                    labels['project'].configure(text_color=labels['project_color'])  # Original color

    def _on_ok(self):
        """Handle Connect button click"""
        if 0 <= self.selected_index < len(self.instances):
            self.selected_instance = self.instances[self.selected_index]
        else:
            self.selected_instance = None
        self.root.destroy()

    def _on_cancel(self):
        """Handle Cancel button click"""
        self.selected_instance = None
        self.root.destroy()


def get_e3_instances() -> List[E3InstanceInfo]:
    """
    Get list of running E3.series instances with project information.

    Returns:
        List of E3InstanceInfo objects
    """
    instances = []

    try:
        # Use tasklist to find E3.series processes
        result = subprocess.run(
            ['tasklist', '/FO', 'CSV'],
            capture_output=True,
            text=True,
            shell=True
        )

        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            # Skip header line
            for line in lines[1:]:
                if line.strip() and 'E3.series.exe' in line:
                    # Parse CSV format: "Image Name","PID","Session Name","Session#","Mem Usage"
                    parts = [part.strip('"') for part in line.split('","')]
                    if len(parts) >= 2:
                        try:
                            pid = int(parts[1])

                            # Try to get project information for this instance
                            project_name = _get_project_name_for_instance(pid)

                            instances.append(E3InstanceInfo(
                                process_id=pid,
                                window_title=f"E3.series (PID: {pid})",
                                project_name=project_name
                            ))
                        except ValueError:
                            continue

    except Exception as e:
        logging.getLogger(__name__).debug(f"Failed to get E3 instances: {e}")

    return instances


def _get_project_name_for_instance(pid: int) -> str:
    """
    Try to get the project name for a specific E3.series instance.

    Args:
        pid: Process ID of the E3.series instance

    Returns:
        Project name if found, otherwise a descriptive string
    """
    try:
        # Try to get window titles to identify projects
        import win32gui
        import win32process

        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                if window_pid == pid:
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        windows.append(window_title)
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        # Look for E3.series main window and project-related windows
        main_window = None
        project_windows = []

        for window_title in windows:
            if 'E3.series' in window_title or 'E3.DTM' in window_title:
                if len(window_title) > 15:  # Main window usually has longer title
                    main_window = window_title
                project_windows.append(window_title)

        # Try to extract project name from main window title
        if main_window:
            logger = logging.getLogger(__name__)
            logger.debug(f"PID {pid} main window: {main_window}")

            # Common E3.series title formats observed:
            # "ProjectName.e3s - E3.DTM Panel Professional - [Sheet info]"
            # "ProjectName.e3s - E3.DTM Panel Professional"
            # "E3.series - ProjectName"
            # "ProjectName - E3.series"

            # Pattern 1: "ProjectName.e3s - E3.DTM Panel Professional..."
            if ".e3s - E3.DTM" in main_window or ".e3s - E3.series" in main_window:
                # Extract everything before " - E3."
                if " - E3." in main_window:
                    project_name = main_window.split(" - E3.")[0].strip()
                    if project_name:
                        return project_name

            # Pattern 2: "ProjectName - E3.series"
            elif main_window.endswith(" - E3.series"):
                project_name = main_window.replace(" - E3.series", "").strip()
                if project_name:
                    return project_name

            # Pattern 3: "E3.series - ProjectName"
            elif main_window.startswith("E3.series - "):
                project_name = main_window.replace("E3.series - ", "").strip()
                # Remove sheet/page information if present
                if " - " in project_name:
                    project_name = project_name.split(" - ")[0]
                if project_name and project_name not in ["Untitled", "New"]:
                    return project_name

            # Pattern 4: General parsing - look for meaningful project names
            elif " - " in main_window:
                parts = main_window.split(" - ")
                for part in parts:
                    part = part.strip()
                    # Look for parts that look like project names
                    if (part and
                        part not in ["E3.series", "E3.DTM Panel Professional", "Untitled", "New", "Document"] and
                        not part.startswith("Sheet") and
                        not part.startswith("Page") and
                        not part.startswith("[") and
                        len(part) > 2):
                        return part

        # Check if any windows suggest a project is open
        has_project_windows = any(
            window for window in windows
            if any(keyword in window.lower() for keyword in ['project', 'sheet', 'page', 'drawing'])
            and 'E3' in window
        )

        if has_project_windows:
            return "Project Open"
        elif windows:
            return "No Project"
        else:
            return "Instance Not Responding"

    except Exception as e:
        logging.getLogger(__name__).debug(f"Failed to get project name for PID {pid}: {e}")
        return "Unknown Project"


def get_e3_instance_pid(logger: Optional[logging.Logger] = None) -> Optional[int]:
    """
    Get the PID of an E3.series instance to connect to.

    If multiple instances are running, shows a simple console-based selector to avoid threading issues.
    If only one instance is running, returns its PID directly.
    If no instances are running, shows an error and returns None.

    Args:
        logger: Optional logger instance

    Returns:
        PID of selected E3.series instance, or None if cancelled/error
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # Get list of E3.series instances
    instances = get_e3_instances()

    if len(instances) == 0:
        # No instances found
        logger.error("No E3.series instances found")
        return None

    elif len(instances) == 1:
        # Single instance - return its PID directly
        instance = instances[0]
        logger.info(f"Found single E3.series instance: PID {instance.process_id}")
        return instance.process_id

    else:
        # Multiple instances - use console selection to avoid threading issues
        logger.info(f"Found {len(instances)} E3.series instances")
        logger.info("Available instances:")
        for i, instance in enumerate(instances):
            logger.info(f"  {i+1}. {instance}")

        # For now, auto-select the first instance to avoid GUI threading issues
        # TODO: Implement proper main-thread GUI selection
        selected = instances[0]
        logger.info(f"Auto-selected first instance: PID {selected.process_id}")
        return selected.process_id


def get_e3_instance_pid_with_gui(logger: Optional[logging.Logger] = None) -> Optional[int]:
    """
    Get the PID of an E3.series instance to connect to using GUI selector.

    This version shows the GUI selector and should only be called from the main thread.

    Args:
        logger: Optional logger instance

    Returns:
        PID of selected E3.series instance, or None if cancelled/error
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # Get list of E3.series instances
    instances = get_e3_instances()

    if len(instances) == 0:
        # No instances found
        logger.error("No E3.series instances found")
        messagebox.showerror(
            "E3.series Not Found",
            "No running E3.series instances found.\n"
            "Please start E3.series with a project open and try again."
        )
        return None

    elif len(instances) == 1:
        # Single instance - return its PID directly
        instance = instances[0]
        logger.info(f"Found single E3.series instance: PID {instance.process_id}")
        return instance.process_id

    else:
        # Multiple instances - show GUI selector
        logger.info(f"Found {len(instances)} E3.series instances")
        selector = E3InstanceSelector(instances, logger)
        selected = selector.show_selector()

        if selected is None:
            logger.info("User cancelled E3 instance selection")
            return None

        logger.info(f"User selected: PID {selected.process_id}")
        return selected.process_id


def connect_to_e3_with_pid(pid: Optional[int] = None, logger: Optional[logging.Logger] = None):
    """
    Connect to E3.series using a specific PID or auto-select if PID is None.

    Args:
        pid: Process ID of E3.series instance to connect to, or None to auto-select
        logger: Optional logger instance

    Returns:
        E3 application object if successful, None otherwise
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # If no PID provided, get one using the selector
    if pid is None:
        pid = get_e3_instance_pid(logger)
        if pid is None:
            return None

    try:
        # Initialize COM
        import pythoncom
        pythoncom.CoInitialize()

        # Connect to E3.series
        # Note: The e3series library connects to the active instance
        # For PID-specific connection, you might need to use win32com.client
        # with specific connection methods if available
        import e3series
        app = e3series.Application()

        # Verify connection by creating a job object
        job = app.CreateJobObject()
        if job:
            logger.info(f"Successfully connected to E3.series (target PID: {pid})")
            return app
        else:
            logger.error("Failed to create job object - no project may be open")
            messagebox.showerror(
                "E3.series Connection Error",
                "Connected to E3.series but no project appears to be open.\n"
                "Please open a project in E3.series and try again."
            )
            return None

    except Exception as e:
        logger.error(f"Failed to connect to E3.series (PID {pid}): {e}")
        messagebox.showerror(
            "E3.series Connection Error",
            f"Failed to connect to E3.series:\n{str(e)}\n\n"
            "Please ensure E3.series is running with a project open."
        )
        return None
